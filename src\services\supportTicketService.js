import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { SUPPORT_TICKET_URLS } from '@/helper/constants/urls';

export const supportTicketService = {
  // Get support ticket dashboard data
  getDashboardData: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        SUPPORT_TICKET_URLS?.SUPPORT_TICKET_DASHBOARD
      );
      if (status === 200) {
        return data?.data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getTicketsList: async (search, page, limit, filters, sort) => {
    try {
      // Build query parameters using URLSearchParams
      const params = new URLSearchParams();

      // Basic parameters
      if (page) params.append('page', page.toString());
      if (limit) params.append('limit', limit.toString());
      if (search) params.append('search', search);

      // Filter parameters
      if (filters?.ticket_status)
        params.append('ticket_status', filters.ticket_status);
      if (filters?.ticket_priority)
        params.append('ticket_priority', filters.ticket_priority);
      if (filters?.ticket_module)
        params.append('ticket_module', filters.ticket_module);
      if (filters?.ticket_type)
        params.append('ticket_type', filters.ticket_type);
      if (filters?.assigned_to_user_id)
        params.append('assigned_to_user_id', filters.assigned_to_user_id);

      // Sort parameters
      if (sort?.key && sort?.value) {
        params.append('sort_by', sort.key);
        params.append('sort_order', sort.value);
      }

      const { status, data } = await axiosInstance.get(
        `${SUPPORT_TICKET_URLS?.GET_ALL_TICKET_LIST}?${params.toString()}`
      );

      if (status === 200) {
        return {
          tickets: data?.data || [],
          totalCount: data?.pagination?.total || 0,
          pagination: data?.pagination || null,
        };
      }

      return {
        tickets: [],
        totalCount: 0,
        pagination: null,
      };
    } catch (error) {
      console.error('Error fetching tickets list:', error);
      throw error;
    }
  },

  // Create new support ticket
  createTicket: async (ticketData, files = []) => {
    try {
      // Create FormData for file uploads
      const formData = new FormData();

      // Add ticket data
      formData.append('ticket_title', ticketData?.Subject);
      formData.append('ticket_description', ticketData?.description);
      formData.append('ticket_module', ticketData?.category);
      formData.append('ticket_type', ticketData?.issueType);
      formData.append('ticket_priority', ticketData?.priority);
      formData.append('support_pin', ticketData.supportPin);
      formData.append('name', ticketData.Name);
      formData.append('email', ticketData.Email);
      formData.append('phone_number', ticketData.PhoneNumber);

      // Add files if any
      if (files && files?.length > 0) {
        files.forEach((file) => {
          if (file?.file) {
            formData.append(`ticketFiles`, file?.file);
          }
        });
      }

      const { status, data } = await axiosInstance.post(
        SUPPORT_TICKET_URLS?.CREATE_TICKET,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (status === 200 || status === 201) {
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error creating ticket:', error);
      throw error;
    }
  },

  // Get individual ticket details by ID
  getTicketDetails: async (ticketId) => {
    try {
      const { status, data } = await axiosInstance.get(
        `${SUPPORT_TICKET_URLS?.GET_SINGLE_TICKET_DETAILS}/${ticketId}`
      );

      if (status === 200) {
        return data?.data || data;
      }
      return null;
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      throw error;
    }
  },

  // Delete support ticket by ID
  deleteTicket: async (ticketId) => {
    try {
      const { status, data } = await axiosInstance.delete(
        `${SUPPORT_TICKET_URLS?.DELETE_TICKET}/${ticketId}`
      );

      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error deleting ticket:', error);
      throw error;
    }
  },

  // Update support ticket by ID
  updateTicket: async (ticketId, updateData) => {
    try {
      const { status, data } = await axiosInstance.put(
        SUPPORT_TICKET_URLS?.UPDATE_TICKET,
        {
          ticket_id: ticketId,
          ...updateData,
        }
      );
      if (status === 200 || status === 201) {
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error updating ticket:', error);
      throw error;
    }
  },

  // Get ticket conversation messages
  getTicketConversation: async (ticketId, params = {}) => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      // Default parameters
      queryParams.append(
        'include_private',
        params?.include_private !== undefined
          ? params?.include_private
          : 'false'
      );
      queryParams.append('page', params?.page || '1');
      queryParams.append('limit', params?.limit || '10');

      const { status, data } = await axiosInstance.get(
        `${SUPPORT_TICKET_URLS?.GET_TICKET_CONVERSATION}/${ticketId}?${queryParams.toString()}`
      );

      if (status === 200) {
        return {
          data: data?.data || [],
          pagination: data?.pagination || null,
          totalCount: data?.pagination?.totalItems || 0,
        };
      }
      return {
        data: [],
        pagination: null,
        totalCount: 0,
      };
    } catch (error) {
      console.error('Error fetching ticket conversation:', error);
      throw error;
    }
  },

  // Add message to ticket conversation
  addTicketMessage: async (ticketId, messageData) => {
    try {
      const { status, data } = await axiosInstance.post(
        `${SUPPORT_TICKET_URLS?.ADD_TICKET_MESSAGE}/${ticketId}`,
        messageData
      );

      if (status === 200 || status === 201) {
        return data;
      }
      return null;
    } catch (error) {
      console.error('Error adding ticket message:', error);
      throw error;
    }
  },

  // Get ticket history
  getTicketHistory: async (ticketId, params = {}) => {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();

      // Add optional parameters
      if (params?.filter) queryParams.append('filter', params.filter);
      if (params?.page) queryParams.append('page', params.page.toString());
      queryParams.append('limit', params?.limit || '20');

      const url = `${SUPPORT_TICKET_URLS?.TICKET_HISTORY}/${ticketId}`;
      const apiUrl = queryParams.toString()
        ? `${url}?${queryParams.toString()}`
        : url;

      const { status, data } = await axiosInstance.get(apiUrl);

      if (status === 200) {
        return {
          data: data?.data || [],
          pagination: data?.pagination || null,
          totalCount: data?.pagination?.totalItems || 0,
        };
      }
      return {
        data: [],
        pagination: null,
        totalCount: 0,
      };
    } catch (error) {
      console.error('Error fetching ticket history:', error);
      throw error;
    }
  },
};
