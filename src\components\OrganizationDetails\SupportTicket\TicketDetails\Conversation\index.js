import React, { useState, useEffect, useRef } from 'react';
import { EmptyConversationIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import {
  DateFormat,
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { supportTicketService } from '@/services/supportTicketService';
import SendIcon from '@mui/icons-material/Send';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import DownloadIcon from '@mui/icons-material/Download';
import HeaderImage from '@/components/UI/ImageSecurity';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import './conversation.scss';

export default function Conversation({ ticketId, ticket }) {
  const [newMessage, setNewMessage] = useState('');
  const [isInternalNote, setIsInternalNote] = useState(false);
  const [conversationData, setConversationData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const conversationRef = useRef(null);

  // Check if user is staff to hide internal notes
  const isStaff = checkOrganizationRole('staff');
  // Check if user is org_master to show comment box
  const isOrgMaster = checkOrganizationRole('org_master');
  // Check if user is super_admin to show internal note checkbox
  const isSuperAdmin = checkOrganizationRole('super_admin');

  // Get display name for ticket owner
  const getTicketOwnerDisplayName = () => {
    if (ticket?.creator_full_name) {
      return ticket.creator_full_name;
    }
    return 'User'; // Fallback to 'User' if no name available
  };

  // Fetch conversation data when ticketId changes
  useEffect(() => {
    if (ticketId) {
      fetchConversationData(1, true);
    }
  }, [ticketId]);

  // Add scroll event listener for infinite scroll
  useEffect(() => {
    const conversationContainer = conversationRef.current;
    if (conversationContainer) {
      conversationContainer.addEventListener('scroll', handleScroll);
      return () => {
        conversationContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [hasMore, loading, currentPage]); // Dependencies for the scroll handler

  // Function to load more messages
  const loadMoreMessages = () => {
    if (hasMore && !loading) {
      fetchConversationData(currentPage + 1, false);
    }
  };

  // Scroll handler for infinite scroll
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e?.target || {};
    // Load more when user scrolls to 80% of the container
    if (scrollTop + clientHeight >= scrollHeight * 0.8 && hasMore && !loading) {
      loadMoreMessages();
    }
  };

  const fetchConversationData = async (page = 1, reset = false) => {
    if (!ticketId) return;

    setLoading(true);
    try {
      const params = {
        include_private: !isStaff, // true if not staff (to show internal notes), false if staff
        page: page,
        limit: 10,
      };

      const response = await supportTicketService.getTicketConversation(
        ticketId,
        params
      );

      if (response?.data) {
        if (reset || page === 1) {
          setConversationData(response?.data);
        } else {
          setConversationData((prev) => [...prev, ...response?.data]);
        }

        setCurrentPage(page);
        setHasMore(response.pagination?.hasNextPage || false); // Use pagination info from API
      } else {
        if (reset || page === 1) {
          setConversationData([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching conversation:', error);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch conversation'
      );
      if (reset || page === 1) {
        setConversationData([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Filter out internal notes for staff users
  const filteredConversationData = isStaff
    ? conversationData?.data?.filter((message) => !message?.is_private) || []
    : conversationData?.data || [];

  const handleSendMessage = async () => {
    if (!newMessage?.trim() || !ticketId || sendingMessage) return;

    const messageText = newMessage?.trim();
    setNewMessage(''); // Clear input immediately for better UX
    setSendingMessage(true);

    try {
      const messageData = {
        message_text: messageText,
        is_private: isInternalNote,
      };

      const response = await supportTicketService.addTicketMessage(
        ticketId,
        messageData
      );

      if (response) {
        // Add the new message immediately to show it right away
        if (response?.data) {
          setConversationData((prev) => ({
            ...prev,
            data: [...(prev?.data || []), response?.data],
          }));
        }

        // Show the success message from API response
        setApiMessage(
          'success',
          response?.message || 'Message sent successfully'
        );

        // Refresh conversation data in background to ensure sync
        fetchConversationData(1, true).catch((error) => {
          console.error('Background refresh failed:', error);
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to send message'
      );
      // Restore the message text if sending failed
      setNewMessage(messageText);
    } finally {
      setSendingMessage(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e?.key === 'Enter' && !e?.shiftKey && !sendingMessage) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle download for attachments
  const handleDownload = async (fileUrl, fileName) => {
    try {
      if (!fileUrl || !fileName) {
        console.error('Invalid file URL or filename');
        return;
      }

      if (fileUrl.startsWith('http')) {
        const response = await fetch(fileUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);
      } else {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Download failed:', error);
      window.open(fileUrl, '_blank');
    }
  };

  // Render attachments using Change Request UI style
  const renderAttachments = (attachments) => {
    if (!attachments || attachments.length === 0) return null;

    return (
      <Box className="message-attachments pt8">
        <Typography className="body-text fw500 pb4">Attached files</Typography>
        <Box className="file-grid-container">
          {attachments.map((attachment, index) => {
            const fileName =
              attachment?.attachment_name ||
              attachment?.item_name ||
              attachment?.name ||
              'Unknown file';
            const fileUrl =
              attachment?.download_url ||
              attachment?.url ||
              attachment?.preview;
            const mimeType =
              attachment?.mime_type ||
              attachment?.item_mime_type ||
              attachment?.type ||
              '';
            const isImage = mimeType.startsWith('image/');

            return (
              <Box
                key={index}
                className="selected-files selected-view-files file-grid-item"
              >
                <Box className="file-name">
                  <Box className="d-flex align-center gap-sm">
                    <InsertDriveFileIcon className="file-icon" />
                    <Typography className="title-text text-ellipsis-line text-capital">
                      {fileName}
                    </Typography>
                  </Box>
                </Box>
                {isImage && fileUrl && (
                  <HeaderImage
                    type="url"
                    imageUrl={fileUrl}
                    Content={<RemoveRedEyeIcon />}
                    className="d-flex align-center"
                  />
                )}
                <DownloadIcon
                  className="ml8"
                  onClick={() => handleDownload(fileUrl, fileName)}
                  sx={{ cursor: 'pointer' }}
                />
              </Box>
            );
          })}
        </Box>
      </Box>
    );
  };

  if (loading) {
    return (
      <Box className="convesation-wrap d-flex flex-col align-center justify-center text-align pb32">
        <ContentLoader />
      </Box>
    );
  }

  if (!filteredConversationData?.length) {
    return (
      <Box className="conversation-container">
        <Box className="convesation-wrap d-flex flex-col align-center justify-center text-align pb32">
          <EmptyConversationIcon className="conversation-icon" />
          <Box>
            <Typography className="conversation-text body-sm">
              No Conversation available
            </Typography>
          </Box>
        </Box>

        {/* Message input for org_master users even when no conversation exists */}
        {(!isStaff || isOrgMaster) && (
          <Box className="message-input-container">
            <Box className="conversation-input-wrapper d-flex align-center gap-sm">
              <Box className="conversation-input-container">
                <CustomTextField
                  className="conversation-input"
                  fullWidth
                  placeholder="Add a comment..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e?.target?.value)}
                  onKeyDown={handleKeyDown}
                  variant="outlined"
                  maxRows={3}
                  minRows={3}
                  multiline
                  disabled={sendingMessage}
                />
              </Box>
              <CustomButton
                startIcon={<SendIcon />}
                isIconOnly
                onClick={handleSendMessage}
                disabled={!newMessage?.trim() || sendingMessage}
                className="conversation-send-button-icon"
                variant="outlined"
              />
            </Box>
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Box className="conversation-container">
      <Box className="conversation-messages" ref={conversationRef}>
        {filteredConversationData?.map((message) => (
          <Box
            key={message?.id}
            className={`message-item ${message?.is_private ? 'internal-note' : ''}`}
          >
            <Box className="message-header">
              <Typography className="sender-name body-sm">
                {message?.message_type === 'AGENT'
                  ? 'Support Team'
                  : getTicketOwnerDisplayName()}
                {message?.is_private && (
                  <span className="internal-note-badge sub-title-text">
                    Internal Note
                  </span>
                )}
              </Typography>
              <Typography className="message-timestamp body-xs">
                {DateFormat(message?.created_at, 'datesWithhour')}
              </Typography>
            </Box>
            <Box className="message-content">
              <Typography className="message-text body-sm">
                {message?.message_text}
              </Typography>

              {/* Render attachments using Change Request UI */}
              {renderAttachments(message?.attachments || message?.files || [])}
            </Box>
          </Box>
        ))}

        {/* Loading indicator for infinite scroll */}
        {loading && hasMore && (
          <Box className="load-more-container d-flex justify-center mt16">
            <ContentLoader />
          </Box>
        )}
      </Box>

      {/* Internal note checkbox - only visible for super admin users */}
      {isSuperAdmin && (
        <Box className="internal-note-checkbox d-flex align-center">
          <CustomCheckbox
            checked={isInternalNote}
            onChange={(e) => setIsInternalNote(e?.target?.checked)}
            name="internal_note"
          />
          <Box className="d-flex align-center gap-sm">
            {isInternalNote ? (
              <VisibilityIcon className="visibility-icon active" />
            ) : (
              <VisibilityOffIcon className="visibility-icon" />
            )}
            <Typography className="checkbox-label body-sm">
              Internal note (visible to agents only)
            </Typography>
          </Box>
        </Box>
      )}

      {/* Message input - show for org_master and non-staff users */}
      {(!isStaff || isOrgMaster) && (
        <Box className="message-input-container">
          <Box className="conversation-input-wrapper d-flex align-center gap-sm">
            <Box className="conversation-input-container">
              <CustomTextField
                className="conversation-input"
                fullWidth
                placeholder="Add a comment..."
                value={newMessage}
                onChange={(e) => setNewMessage(e?.target?.value)}
                onKeyDown={handleKeyDown}
                variant="outlined"
                maxRows={3}
                minRows={3}
                multiline
                disabled={sendingMessage}
              />
            </Box>
            <CustomButton
              startIcon={<SendIcon />}
              isIconOnly
              onClick={handleSendMessage}
              disabled={!newMessage?.trim() || sendingMessage}
              className="conversation-send-button-icon"
              variant="outlined"
            />
          </Box>
        </Box>
      )}
    </Box>
  );
}
