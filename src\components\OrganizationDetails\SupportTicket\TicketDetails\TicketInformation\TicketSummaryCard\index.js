import React from 'react';
import { Box, Typography } from '@mui/material';
import HeaderImage from '@/components/UI/ImageSecurity';
import Profile from '../../../../../../../public/images/Companylogo.png';
import { DateFormat } from '@/helper/common/commonFunctions';
import './ticketSummaryCard.scss';

export default function TicketSummaryCard({
  ticket,
  getDisplayValue,
  getTicketOwnerDisplayName,
  classificationOptions,
  priorityOptions,
  statusOptions,
}) {
  // Function to get status chip class
  const getStatusChipClass = (status) => {
    const statusMap = {
      open: 'draft',
      in_progress: 'ongoing',
      resolved: 'success',
      closed: 'closed',
      pending: 'draft',
    };
    return statusMap[status] || 'draft';
  };

  // Function to get priority chip class
  const getPriorityChipClass = (priority) => {
    const priorityMap = {
      high: 'failed',
      urgent: 'failed',
      medium: 'status-yellow',
      low: 'success',
      none: 'draft',
    };
    return priorityMap[priority] || 'draft';
  };

  return (
    <Box className="ticket-summary-card">
      <Box className="ticket-card-container">
        {/* Header with title and status - Row 1 */}
        <Box className="ticket-header-top">
          <Box className="d-flex align-center gap-sm">
            <Typography className="body-text fw600">#{ticket?.id}</Typography>
            <Typography className="body-text fw600">
              {ticket?.ticket_title}
            </Typography>
            {ticket?.ticket_status && (
              <span
                className={`${getStatusChipClass(ticket?.ticket_status)} sub-title-text fw500`}
              >
                {getDisplayValue(ticket?.ticket_status, statusOptions)}
              </span>
            )}
          </Box>

          {ticket?.ticket_priority && (
            <span
              className={`${getPriorityChipClass(ticket.ticket_priority)} caption-text fw600`}
            >
              {getDisplayValue(
                ticket?.ticket_priority,
                priorityOptions
              )?.toUpperCase()}
            </span>
          )}
        </Box>

        {/* Description - Row 2 */}
        <Typography className="ticket-description body-sm-regular">
          {ticket?.ticket_description ||
            'User unable to access their account after password reset'}
        </Typography>

        {/* User info with avatar, name, date, issue - Row 3 */}
        <Box className="ticket-user-info">
          <HeaderImage
            imageUrl={Profile}
            type="avtar"
            className="ticket-meta-avatar"
            IsExternal={false}
          />
          <Typography className="ticket-meta-name sub-title-text fw500">
            {getTicketOwnerDisplayName()}
          </Typography>
          <Typography className="ticket-meta-date sub-title-text">
            {ticket?.created_at &&
              DateFormat(ticket?.created_at, 'datesWithhour')}
          </Typography>
          <Typography className="ticket-meta-issue sub-title-text">
            {ticket?.ticket_module
              ? getDisplayValue(ticket?.ticket_module, classificationOptions)
              : 'Account Issue'}
          </Typography>
        </Box>

        {/* Assigned info - Row 4 */}
        <Box className="ticket-assigned-section">
          <Typography className="ticket-assigned-text sub-title-text">
            Assigned to:{' '}
            <span className="ticket-assigned-name fw500">
              {ticket?.assigned_full_name || 'Sarah Johnson'}
            </span>
          </Typography>
        </Box>
      </Box>
    </Box>
  );
}
