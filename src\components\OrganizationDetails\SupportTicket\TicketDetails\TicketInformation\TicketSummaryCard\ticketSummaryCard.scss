
// TicketSummaryCard styles (unique, non-conflicting)
.ticket-summary-card {
  border: var(--normal-sec-border);
  background: var(--color-white);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  box-shadow: var(--box-shadow-xs);
  transition: box-shadow 0.2s;

  .ticket-card-container {
    width: 100%;
  }

  // Row 1: Ticket Number + Title + Status + Priority
  .ticket-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .ticket-title {
    color: var(--text-color-black);
    margin: 0;
    flex: 1;
  }

  .ticket-status-badge {
    background: var(--color-light-blue);
    color: var(--color-primary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xs) var(--spacing-md);
    display: inline-block;
    white-space: nowrap;

    &.status-in_progress {
      background: var(--color-light-blue);
      color: var(--color-primary);
    }

    &.status-open {
      background: var(--color-light-champagne);
      color: var(--color-muted-mustard);
    }

    &.status-closed {
      background: var(--color-success-opacity);
      color: var(--color-success);
    }

    &.status-resolved {
      background: var(--color-success-opacity);
      color: var(--color-success);
    }
  }

  .ticket-priority {
    background: var(--color-danger-background);
    color: var(--color-danger);
    letter-spacing: var(--letter-spacing-wide);
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--border-radius-xs);
    display: inline-block;
    white-space: nowrap;
  }
  .priority-high {
    background: var(--color-danger-background);
    color: var(--color-danger);
  }

  .priority-medium {
    background: var(--color-warning-opacity);
    color: var(--color-warning);
  }

  .priority-low {
    background: var(--color-success-opacity);
    color: var(--color-success);
  }

  .priority-urgent {
    background: var(--color-danger-background);
    color: var(--color-danger);
  }

  .priority-none {
    background: var(--color-secondary);
    color: var(--text-color-slate-gray);
  }

  // Row 2: Description
  .ticket-description {
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-lg);
  }

  // Row 3: User info (avatar + name + date + issue)
  .ticket-user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
  }

  .ticket-meta-avatar {
    width: var(--icon-size-sm);
    height: var(--icon-size-sm);
    border-radius: var(--border-radius-full);
    object-fit: cover;
  }

  .ticket-meta-name {
    color: var(--text-color-black);
  }

  .ticket-meta-date {
    color: var(--text-color-slate-gray);
  }

  .ticket-meta-category {
    color: var(--text-color-slate-gray);
  }

  // Row 4: Assigned info
  .ticket-assigned-section {
    display: flex;
    align-items: center;
  }

  .ticket-assigned-text {
    color: var(--text-color-slate-gray);

    .ticket-assigned-name {
      color: var(--color-primary);
    }
  }
}